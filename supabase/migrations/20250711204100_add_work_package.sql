create table "public"."work_package" (
	"work_package_id" uuid not null default gen_random_uuid(),
	"name" text not null,
	"description" text,
	"project_id" uuid not null,
	"parent_work_package_id" uuid,
	"purchase_order_id" uuid,
	"wbs_library_item_id" uuid not null,
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."work_package" enable row level security;

create table "public"."work_package_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default now(),
	"old_values" jsonb,
	"new_values" jsonb,
	"work_package_id" uuid,
	"name" text,
	"description" text,
	"project_id" uuid,
	"parent_work_package_id" uuid,
	"purchase_order_id" uuid,
	"wbs_library_item_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."work_package_audit" enable row level security;

CREATE INDEX work_package_audit_changed_at_idx ON public.work_package_audit USING btree (changed_at);

CREATE INDEX work_package_audit_changed_by_idx ON public.work_package_audit USING btree (changed_by);

CREATE INDEX work_package_audit_operation_type_idx ON public.work_package_audit USING btree (operation_type);

CREATE UNIQUE INDEX work_package_audit_pkey ON public.work_package_audit USING btree (audit_id);

CREATE INDEX work_package_audit_project_id_idx ON public.work_package_audit USING btree (project_id);

CREATE INDEX work_package_audit_work_package_id_idx ON public.work_package_audit USING btree (work_package_id);

CREATE INDEX work_package_parent_work_package_id_idx ON public.work_package USING btree (parent_work_package_id);

CREATE UNIQUE INDEX work_package_pkey ON public.work_package USING btree (work_package_id);

CREATE INDEX work_package_project_id_idx ON public.work_package USING btree (project_id);

CREATE INDEX work_package_purchase_order_id_idx ON public.work_package USING btree (purchase_order_id);

CREATE INDEX work_package_wbs_library_item_id_idx ON public.work_package USING btree (wbs_library_item_id);

alter table "public"."work_package"
add constraint "work_package_pkey" PRIMARY KEY using index "work_package_pkey";

alter table "public"."work_package_audit"
add constraint "work_package_audit_pkey" PRIMARY KEY using index "work_package_audit_pkey";

alter table "public"."work_package"
add constraint "work_package_parent_work_package_id_fkey" FOREIGN KEY (parent_work_package_id) REFERENCES work_package (work_package_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."work_package" validate constraint "work_package_parent_work_package_id_fkey";

alter table "public"."work_package"
add constraint "work_package_project_id_fkey" FOREIGN KEY (project_id) REFERENCES project (project_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."work_package" validate constraint "work_package_project_id_fkey";

alter table "public"."work_package"
add constraint "work_package_purchase_order_id_fkey" FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (purchase_order_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."work_package" validate constraint "work_package_purchase_order_id_fkey";

alter table "public"."work_package"
add constraint "work_package_wbs_library_item_id_fkey" FOREIGN KEY (wbs_library_item_id) REFERENCES wbs_library_item (wbs_library_item_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."work_package" validate constraint "work_package_wbs_library_item_id_fkey";

alter table "public"."work_package_audit"
add constraint "work_package_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."work_package_audit" validate constraint "work_package_audit_changed_by_fkey";

alter table "public"."work_package_audit"
add constraint "work_package_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."work_package_audit" validate constraint "work_package_audit_operation_type_check";

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.audit_work_package_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, old_values,
            work_package_id, name, description, project_id, parent_work_package_id,
            purchase_order_id, wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.work_package_id, OLD.name, OLD.description, OLD.project_id, OLD.parent_work_package_id,
            OLD.purchase_order_id, OLD.wbs_library_item_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            work_package_id, name, description, project_id, parent_work_package_id,
            purchase_order_id, wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.work_package_id, NEW.name, NEW.description, NEW.project_id, NEW.parent_work_package_id,
            NEW.purchase_order_id, NEW.wbs_library_item_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, new_values,
            work_package_id, name, description, project_id, parent_work_package_id,
            purchase_order_id, wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.work_package_id, NEW.name, NEW.description, NEW.project_id, NEW.parent_work_package_id,
            NEW.purchase_order_id, NEW.wbs_library_item_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_accessible_work_packages (project_id_param uuid) RETURNS TABLE (
	work_package_id uuid,
	name text,
	description text,
	project_id uuid,
	parent_work_package_id uuid,
	parent_work_package_name text,
	purchase_order_id uuid,
	purchase_order_number text,
	wbs_library_item_id uuid,
	wbs_code text,
	wbs_description text,
	created_at timestamp with time zone,
	updated_at timestamp with time zone
) LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return work packages for the project with related information
	RETURN QUERY
	SELECT
		wp.work_package_id,
		wp.name,
		wp.description,
		wp.project_id,
		wp.parent_work_package_id,
		pwp.name AS parent_work_package_name,
		wp.purchase_order_id,
		po.po_number AS purchase_order_number,
		wp.wbs_library_item_id,
		wli.code AS wbs_code,
		wli.description AS wbs_description,
		wp.created_at,
		wp.updated_at
	FROM public.work_package wp
	LEFT JOIN public.work_package pwp ON wp.parent_work_package_id = pwp.work_package_id
	LEFT JOIN public.purchase_order po ON wp.purchase_order_id = po.purchase_order_id
	LEFT JOIN public.wbs_library_item wli ON wp.wbs_library_item_id = wli.wbs_library_item_id
	WHERE wp.project_id = project_id_param
	ORDER BY wp.name;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_purchase_orders_for_work_package (project_id_param uuid) RETURNS TABLE (
	purchase_order_id uuid,
	po_number text,
	description text,
	vendor_name text
) LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return purchase orders for the project with vendor information
	RETURN QUERY
	SELECT
		po.purchase_order_id,
		po.po_number,
		po.description,
		v.name AS vendor_name
	FROM public.purchase_order po
	LEFT JOIN public.vendor v ON po.vendor_id = v.vendor_id
	WHERE po.project_id = project_id_param
	ORDER BY po.po_number;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_wbs_items_for_work_package (project_id_param uuid) RETURNS TABLE (
	wbs_library_item_id uuid,
	code text,
	description text,
	level integer,
	parent_item_id uuid
) LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
	v_client_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Get the client_id for the project
	SELECT p.client_id INTO v_client_id
	FROM public.project p
	WHERE p.project_id = project_id_param;

	-- Return WBS library items accessible for this project
	-- This includes: Standard items, client-level custom items, and project-level custom items
	RETURN QUERY
	SELECT
		wli.wbs_library_item_id,
		wli.code,
		wli.description,
		wli.level,
		wli.parent_item_id
	FROM public.wbs_library_item wli
	WHERE
		-- Standard items (globally accessible)
		wli.item_type = 'Standard'::public.wbs_item_type
		OR
		-- Client-level custom items
		(wli.item_type = 'Custom'::public.wbs_item_type AND wli.client_id = v_client_id AND wli.project_id IS NULL)
		OR
		-- Project-level custom items
		(wli.item_type = 'Custom'::public.wbs_item_type AND wli.project_id = project_id_param)
	ORDER BY wli.code;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_work_packages_for_parent_selection (
	project_id_param uuid,
	exclude_work_package_id uuid DEFAULT NULL::uuid
) RETURNS TABLE (
	work_package_id uuid,
	name text,
	level integer,
	parent_work_package_id uuid
) LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return work packages for parent selection, excluding the specified work package and its descendants
	-- This prevents circular references in the hierarchy
	RETURN QUERY
	WITH RECURSIVE excluded_descendants AS (
		-- Base case: the work package to exclude
		SELECT wp.work_package_id
		FROM public.work_package wp
		WHERE wp.work_package_id = exclude_work_package_id

		UNION ALL

		-- Recursive case: children of excluded work packages
		SELECT wp.work_package_id
		FROM public.work_package wp
		INNER JOIN excluded_descendants ed ON wp.parent_work_package_id = ed.work_package_id
	),
	work_package_levels AS (
		-- Calculate hierarchy levels for proper ordering
		WITH RECURSIVE wp_hierarchy AS (
			-- Root work packages (level 0)
			SELECT
				wp.work_package_id,
				wp.name,
				wp.parent_work_package_id,
				0 as level
			FROM public.work_package wp
			WHERE wp.project_id = project_id_param
				AND wp.parent_work_package_id IS NULL
				AND (exclude_work_package_id IS NULL OR wp.work_package_id NOT IN (SELECT work_package_id FROM excluded_descendants))

			UNION ALL

			-- Child work packages (level + 1)
			SELECT
				wp.work_package_id,
				wp.name,
				wp.parent_work_package_id,
				wph.level + 1
			FROM public.work_package wp
			INNER JOIN wp_hierarchy wph ON wp.parent_work_package_id = wph.work_package_id
			WHERE wp.project_id = project_id_param
				AND (exclude_work_package_id IS NULL OR wp.work_package_id NOT IN (SELECT work_package_id FROM excluded_descendants))
		)
		SELECT * FROM wp_hierarchy
	)
	SELECT
		wpl.work_package_id,
		wpl.name,
		wpl.level,
		wpl.parent_work_package_id
	FROM work_package_levels wpl
	ORDER BY wpl.level, wpl.name;
END;
$function$;

grant delete on table "public"."work_package" to "anon";

grant insert on table "public"."work_package" to "anon";

grant references on table "public"."work_package" to "anon";

grant
select
	on table "public"."work_package" to "anon";

grant trigger on table "public"."work_package" to "anon";

grant
truncate on table "public"."work_package" to "anon";

grant
update on table "public"."work_package" to "anon";

grant delete on table "public"."work_package" to "authenticated";

grant insert on table "public"."work_package" to "authenticated";

grant references on table "public"."work_package" to "authenticated";

grant
select
	on table "public"."work_package" to "authenticated";

grant trigger on table "public"."work_package" to "authenticated";

grant
truncate on table "public"."work_package" to "authenticated";

grant
update on table "public"."work_package" to "authenticated";

grant delete on table "public"."work_package" to "service_role";

grant insert on table "public"."work_package" to "service_role";

grant references on table "public"."work_package" to "service_role";

grant
select
	on table "public"."work_package" to "service_role";

grant trigger on table "public"."work_package" to "service_role";

grant
truncate on table "public"."work_package" to "service_role";

grant
update on table "public"."work_package" to "service_role";

grant delete on table "public"."work_package_audit" to "anon";

grant insert on table "public"."work_package_audit" to "anon";

grant references on table "public"."work_package_audit" to "anon";

grant
select
	on table "public"."work_package_audit" to "anon";

grant trigger on table "public"."work_package_audit" to "anon";

grant
truncate on table "public"."work_package_audit" to "anon";

grant
update on table "public"."work_package_audit" to "anon";

grant delete on table "public"."work_package_audit" to "authenticated";

grant insert on table "public"."work_package_audit" to "authenticated";

grant references on table "public"."work_package_audit" to "authenticated";

grant
select
	on table "public"."work_package_audit" to "authenticated";

grant trigger on table "public"."work_package_audit" to "authenticated";

grant
truncate on table "public"."work_package_audit" to "authenticated";

grant
update on table "public"."work_package_audit" to "authenticated";

grant delete on table "public"."work_package_audit" to "service_role";

grant insert on table "public"."work_package_audit" to "service_role";

grant references on table "public"."work_package_audit" to "service_role";

grant
select
	on table "public"."work_package_audit" to "service_role";

grant trigger on table "public"."work_package_audit" to "service_role";

grant
truncate on table "public"."work_package_audit" to "service_role";

grant
update on table "public"."work_package_audit" to "service_role";

create policy "Users can create work packages for projects they can edit" on "public"."work_package" as permissive for insert to authenticated
with
	check (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	);

create policy "Users can delete work packages for projects they can admin" on "public"."work_package" as permissive for delete to authenticated using (
	current_user_has_entity_role (
		'project'::entity_type,
		project_id,
		'admin'::membership_role
	)
);

create policy "Users can update work packages for projects they can edit" on "public"."work_package" as permissive
for update
	to authenticated using (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	)
with
	check (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	);

create policy "Users can view work packages for accessible projects" on "public"."work_package" as permissive for
select
	to authenticated using (
		current_user_has_entity_access ('project'::entity_type, project_id)
	);

create policy "System can insert work package audit records" on "public"."work_package_audit" as permissive for insert to service_role
with
	check (true);

create policy "Users can view work package audit for accessible projects" on "public"."work_package_audit" as permissive for
select
	to authenticated using (
		current_user_has_entity_access ('project'::entity_type, project_id)
	);

CREATE TRIGGER audit_work_package_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.work_package FOR EACH ROW
EXECUTE FUNCTION audit_work_package_changes ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.work_package FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();
