-- Work Package Table Schema
-- Contains work package information with project-level access control
-- Work packages are hierarchical units of work associated with projects and WBS items
CREATE TABLE IF NOT EXISTS "public"."work_package" (
	"work_package_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"project_id" "uuid" NOT NULL,
	"parent_work_package_id" "uuid",
	"purchase_order_id" "uuid",
	"wbs_library_item_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."work_package" OWNER TO "postgres";

COMMENT ON TABLE "public"."work_package" IS 'Work packages for project work breakdown structure';

COMMENT ON COLUMN "public"."work_package"."name" IS 'Name of the work package';

COMMENT ON COLUMN "public"."work_package"."description" IS 'Detailed description of the work package';

COMMENT ON COLUMN "public"."work_package"."parent_work_package_id" IS 'Parent work package for hierarchical structure';

COMMENT ON COLUMN "public"."work_package"."purchase_order_id" IS 'Associated purchase order if applicable';

COMMENT ON COLUMN "public"."work_package"."wbs_library_item_id" IS 'Associated WBS library item';

-- Primary key constraint
ALTER TABLE ONLY "public"."work_package"
ADD CONSTRAINT "work_package_pkey" PRIMARY KEY ("work_package_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."work_package"
ADD CONSTRAINT "work_package_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."work_package"
ADD CONSTRAINT "work_package_parent_work_package_id_fkey" FOREIGN KEY ("parent_work_package_id") REFERENCES "public"."work_package" ("work_package_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."work_package"
ADD CONSTRAINT "work_package_purchase_order_id_fkey" FOREIGN KEY ("purchase_order_id") REFERENCES "public"."purchase_order" ("purchase_order_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."work_package"
ADD CONSTRAINT "work_package_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "work_package_project_id_idx" ON "public"."work_package" USING "btree" ("project_id");

CREATE INDEX "work_package_parent_work_package_id_idx" ON "public"."work_package" USING "btree" ("parent_work_package_id");

CREATE INDEX "work_package_purchase_order_id_idx" ON "public"."work_package" USING "btree" ("purchase_order_id");

CREATE INDEX "work_package_wbs_library_item_id_idx" ON "public"."work_package" USING "btree" ("wbs_library_item_id");

-- Enable Row Level Security
ALTER TABLE "public"."work_package" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."work_package" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Audit trigger for work_package table (function defined in shared/audit_functions.sql)
CREATE OR REPLACE TRIGGER "audit_work_package_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."work_package" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_work_package_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view work packages for projects they have access to
CREATE POLICY "Users can view work packages for accessible projects" ON "public"."work_package" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);

-- INSERT policies - users can create work packages for projects they have editor access to
CREATE POLICY "Users can create work packages for projects they can edit" ON "public"."work_package" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

-- UPDATE policies - users can update work packages for projects they have editor access to
CREATE POLICY "Users can update work packages for projects they can edit" ON "public"."work_package"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

-- DELETE policies - users can delete work packages for projects they have admin access to
CREATE POLICY "Users can delete work packages for projects they can admin" ON "public"."work_package" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'admin'::"public"."membership_role"
	)
);

-- Table-specific functions
-- Function to get work packages accessible to a user for a specific project
CREATE OR REPLACE FUNCTION "public"."get_accessible_work_packages" ("project_id_param" "uuid") RETURNS TABLE (
	"work_package_id" "uuid",
	"name" "text",
	"description" "text",
	"project_id" "uuid",
	"parent_work_package_id" "uuid",
	"parent_work_package_name" "text",
	"purchase_order_id" "uuid",
	"purchase_order_number" "text",
	"wbs_library_item_id" "uuid",
	"wbs_code" "text",
	"wbs_description" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
) LANGUAGE "plpgsql"
SET
	"search_path" TO '' AS $$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return work packages for the project with related information
	RETURN QUERY
	SELECT
		wp.work_package_id,
		wp.name,
		wp.description,
		wp.project_id,
		wp.parent_work_package_id,
		pwp.name AS parent_work_package_name,
		wp.purchase_order_id,
		po.po_number AS purchase_order_number,
		wp.wbs_library_item_id,
		wli.code AS wbs_code,
		wli.description AS wbs_description,
		wp.created_at,
		wp.updated_at
	FROM public.work_package wp
	LEFT JOIN public.work_package pwp ON wp.parent_work_package_id = pwp.work_package_id
	LEFT JOIN public.purchase_order po ON wp.purchase_order_id = po.purchase_order_id
	LEFT JOIN public.wbs_library_item wli ON wp.wbs_library_item_id = wli.wbs_library_item_id
	WHERE wp.project_id = project_id_param
	ORDER BY wp.name;
END;
$$;

ALTER FUNCTION "public"."get_accessible_work_packages" ("project_id_param" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_accessible_work_packages" ("project_id_param" "uuid") IS 'Returns work packages accessible to the current user for a specific project';

-- Function to get purchase orders for work package selection
CREATE OR REPLACE FUNCTION "public"."get_purchase_orders_for_work_package" ("project_id_param" "uuid") RETURNS TABLE (
	"purchase_order_id" "uuid",
	"po_number" "text",
	"description" "text",
	"vendor_name" "text"
) LANGUAGE "plpgsql" 
SET
	"search_path" TO '' AS $$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return purchase orders for the project with vendor information
	RETURN QUERY
	SELECT
		po.purchase_order_id,
		po.po_number,
		po.description,
		v.name AS vendor_name
	FROM public.purchase_order po
	LEFT JOIN public.vendor v ON po.vendor_id = v.vendor_id
	WHERE po.project_id = project_id_param
	ORDER BY po.po_number;
END;
$$;

ALTER FUNCTION "public"."get_purchase_orders_for_work_package" ("project_id_param" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_purchase_orders_for_work_package" ("project_id_param" "uuid") IS 'Returns purchase orders for work package selection';

-- Function to get WBS library items for work package selection
CREATE OR REPLACE FUNCTION "public"."get_wbs_items_for_work_package" ("project_id_param" "uuid") RETURNS TABLE (
	"wbs_library_item_id" "uuid",
	"code" "text",
	"description" "text",
	"level" integer,
	"parent_item_id" "uuid"
) LANGUAGE "plpgsql" 
SET
	"search_path" TO '' AS $$
DECLARE
	v_user_id UUID;
	v_client_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Get the client_id for the project
	SELECT p.client_id INTO v_client_id
	FROM public.project p
	WHERE p.project_id = project_id_param;

	-- Return WBS library items accessible for this project
	-- This includes: Standard items, client-level custom items, and project-level custom items
	RETURN QUERY
	SELECT
		wli.wbs_library_item_id,
		wli.code,
		wli.description,
		wli.level,
		wli.parent_item_id
	FROM public.wbs_library_item wli
	WHERE
		-- Standard items (globally accessible)
		wli.item_type = 'Standard'::public.wbs_item_type
		OR
		-- Client-level custom items
		(wli.item_type = 'Custom'::public.wbs_item_type AND wli.client_id = v_client_id AND wli.project_id IS NULL)
		OR
		-- Project-level custom items
		(wli.item_type = 'Custom'::public.wbs_item_type AND wli.project_id = project_id_param)
	ORDER BY wli.code;
END;
$$;

ALTER FUNCTION "public"."get_wbs_items_for_work_package" ("project_id_param" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_wbs_items_for_work_package" ("project_id_param" "uuid") IS 'Returns WBS library items for work package selection';

-- Function to get work packages for hierarchical parent selection
CREATE OR REPLACE FUNCTION "public"."get_work_packages_for_parent_selection" (
	"project_id_param" "uuid",
	"exclude_work_package_id" "uuid" DEFAULT NULL
) RETURNS TABLE (
	"work_package_id" "uuid",
	"name" "text",
	"level" integer,
	"parent_work_package_id" "uuid"

SET
	"search_path" TO '' AS $$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return work packages for parent selection, excluding the specified work package and its descendants
	-- This prevents circular references in the hierarchy
	RETURN QUERY
	WITH RECURSIVE excluded_descendants AS (
		-- Base case: the work package to exclude
		SELECT wp.work_package_id
		FROM public.work_package wp
		WHERE wp.work_package_id = exclude_work_package_id

		UNION ALL

		-- Recursive case: children of excluded work packages
		SELECT wp.work_package_id
		FROM public.work_package wp
		INNER JOIN excluded_descendants ed ON wp.parent_work_package_id = ed.work_package_id
	),
	work_package_levels AS (
		-- Calculate hierarchy levels for proper ordering
		WITH RECURSIVE wp_hierarchy AS (
			-- Root work packages (level 0)
			SELECT
				wp.work_package_id,
				wp.name,
				wp.parent_work_package_id,
				0 as level
			FROM public.work_package wp
			WHERE wp.project_id = project_id_param
				AND wp.parent_work_package_id IS NULL
				AND (exclude_work_package_id IS NULL OR wp.work_package_id NOT IN (SELECT work_package_id FROM excluded_descendants))

			UNION ALL

			-- Child work packages (level + 1)
			SELECT
				wp.work_package_id,
				wp.name,
				wp.parent_work_package_id,
				wph.level + 1
			FROM public.work_package wp
			INNER JOIN wp_hierarchy wph ON wp.parent_work_package_id = wph.work_package_id
			WHERE wp.project_id = project_id_param
				AND (exclude_work_package_id IS NULL OR wp.work_package_id NOT IN (SELECT work_package_id FROM excluded_descendants))
		)
		SELECT * FROM wp_hierarchy
	)
	SELECT
		wpl.work_package_id,
		wpl.name,
		wpl.level,
		wpl.parent_work_package_id
	FROM work_package_levels wpl
	ORDER BY wpl.level, wpl.name;
END;
$$;

ALTER FUNCTION "public"."get_work_packages_for_parent_selection" (
	"project_id_param" "uuid",
	"exclude_work_package_id" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_work_packages_for_parent_selection" (
	"project_id_param" "uuid",
	"exclude_work_package_id" "uuid"
) IS 'Returns work packages for hierarchical parent selection, excluding specified work package and its descendants';
