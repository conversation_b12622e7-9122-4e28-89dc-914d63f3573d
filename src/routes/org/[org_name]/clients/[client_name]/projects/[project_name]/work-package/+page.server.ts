import type { Actions, PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import { error, fail } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ params, locals, cookies }) => {
	const { user } = await requireUser(cookies);
	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject(params, cookies);

	// Get project information
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		throw error(404, 'Project not found');
	}

	// Fetch work packages using the RPC function
	const { data: workPackages, error: workPackagesError } = await supabase.rpc(
		'get_accessible_work_packages',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (workPackagesError) {
		console.error('Error fetching work packages:', workPackagesError);
		throw error(500, 'Failed to fetch work packages');
	}

	return {
		project: projectData,
		workPackages: workPackages || [],
	};
};

export const actions: Actions = {
	delete: async ({ request, locals, cookies, params }) => {
		await requireUser(cookies);
		const { supabase } = locals;
		const { org_name, client_name, project_name } = requireProject(params, cookies);

		const formData = await request.formData();
		const workPackageId = formData.get('work_package_id') as string;

		if (!workPackageId) {
			return fail(400, {
				message: { type: 'error', text: 'Work package ID is required' },
			});
		}

		// Check if work package has children
		const { data: childWorkPackages, error: childCheckError } = await supabase
			.from('work_package')
			.select('work_package_id')
			.eq('parent_work_package_id', workPackageId)
			.limit(1);

		if (childCheckError) {
			console.error('Error checking child work packages:', childCheckError);
			return fail(500, {
				message: { type: 'error', text: 'Failed to check work package dependencies' },
			});
		}

		if (childWorkPackages && childWorkPackages.length > 0) {
			return fail(400, {
				message: {
					type: 'error',
					text: 'Cannot delete work package with child work packages. Please delete or reassign child work packages first.',
				},
			});
		}

		// Get work package name for success message
		const { data: workPackageData, error: workPackageError } = await supabase
			.from('work_package')
			.select('name')
			.eq('work_package_id', workPackageId)
			.single();

		if (workPackageError || !workPackageData) {
			console.error('Error fetching work package for deletion:', workPackageError);
			return fail(404, {
				message: { type: 'error', text: 'Work package not found' },
			});
		}

		// Delete the work package
		const { error: deleteError } = await supabase
			.from('work_package')
			.delete()
			.eq('work_package_id', workPackageId);

		if (deleteError) {
			console.error('Error deleting work package:', deleteError);
			return fail(500, {
				message: { type: 'error', text: 'Failed to delete work package' },
			});
		}

		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
				client_name,
			)}/projects/${encodeURIComponent(project_name)}/work-package`,
			{
				type: 'success',
				message: `Work package "${workPackageData.name}" deleted successfully`,
			},
			cookies,
		);
	},
};
