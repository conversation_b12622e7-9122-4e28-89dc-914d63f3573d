<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import * as Card from '$lib/components/ui/card';
	import * as Dialog from '$lib/components/ui/dialog';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { PencilSimple as PencilSimpleIcon, Trash as TrashIcon } from 'phosphor-svelte';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';

	const { data }: { data: PageData } = $props();
	const { workPackage, childWorkPackages } = data;

	let deleteDialogOpen = $state(false);

	function handleEdit() {
		goto(
			`/org/${encodeURIComponent(page.params.org_name)}/clients/${encodeURIComponent(
				page.params.client_name,
			)}/projects/${encodeURIComponent(page.params.project_name)}/work-package/${workPackage.work_package_id}/edit`,
		);
	}

	function handleChildView(childWorkPackageId: string) {
		goto(
			`/org/${encodeURIComponent(page.params.org_name)}/clients/${encodeURIComponent(
				page.params.client_name,
			)}/projects/${encodeURIComponent(page.params.project_name)}/work-package/${childWorkPackageId}`,
		);
	}

	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('en-GB', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	}
</script>

<div class="container mx-auto py-8">
	<div class="mb-6 flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-semibold">{workPackage.name}</h1>
			<p class="text-muted-foreground mt-1">
				Work package in {workPackage.project.name}
			</p>
		</div>
		<div class="flex gap-2">
			<Button onclick={handleEdit} class="gap-2">
				<PencilSimpleIcon class="size-4" />
				Edit
			</Button>
			<Button variant="destructive" onclick={() => (deleteDialogOpen = true)} class="gap-2">
				<TrashIcon class="size-4" />
				Delete
			</Button>
		</div>
	</div>

	<div class="grid gap-6">
		<!-- Basic Information -->
		<Card.Root>
			<Card.Header>
				<Card.Title>Basic Information</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div>
					<h4 class="text-muted-foreground text-sm font-medium">Name</h4>
					<p class="text-sm">{workPackage.name}</p>
				</div>
				{#if workPackage.description}
					<div>
						<h4 class="text-muted-foreground text-sm font-medium">Description</h4>
						<p class="text-sm">{workPackage.description}</p>
					</div>
				{/if}
				<div>
					<h4 class="text-muted-foreground text-sm font-medium">Created</h4>
					<p class="text-sm">{formatDate(workPackage.created_at)}</p>
				</div>
			</Card.Content>
		</Card.Root>

		<!-- WBS Information -->
		<Card.Root>
			<Card.Header>
				<Card.Title>WBS Library Item</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div>
					<h4 class="text-muted-foreground text-sm font-medium">Code</h4>
					<p class="font-mono text-sm">{workPackage.wbs_library_item.code}</p>
				</div>
				<div>
					<h4 class="text-muted-foreground text-sm font-medium">Description</h4>
					<p class="text-sm">{workPackage.wbs_library_item.description}</p>
				</div>
				{#if workPackage.wbs_library_item.cost_scope}
					<div>
						<h4 class="text-muted-foreground text-sm font-medium">Cost Scope</h4>
						<p class="text-sm">{workPackage.wbs_library_item.cost_scope}</p>
					</div>
				{/if}
			</Card.Content>
		</Card.Root>

		<!-- Relationships -->
		<Card.Root>
			<Card.Header>
				<Card.Title>Relationships</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div>
					<h4 class="text-muted-foreground text-sm font-medium">Parent Work Package</h4>
					{#if workPackage.parent_work_package}
						<Button
							variant="link"
							class="h-auto p-0 text-sm"
							onclick={() => {
								if (workPackage.parent_work_package) {
									goto(
										`/org/${encodeURIComponent(page.params.org_name)}/clients/${encodeURIComponent(page.params.client_name)}/projects/${encodeURIComponent(page.params.project_name)}/work-package/${workPackage.parent_work_package.work_package_id}`,
									);
								}
							}}
						>
							{workPackage.parent_work_package.name}
						</Button>
					{:else}
						<p class="text-muted-foreground text-sm">No parent work package</p>
					{/if}
				</div>
				<div>
					<h4 class="text-muted-foreground text-sm font-medium">Purchase Order</h4>
					{#if workPackage.purchase_order}
						<div class="text-sm">
							<p class="font-medium">{workPackage.purchase_order.po_number}</p>
							{#if workPackage.purchase_order.vendor}
								<p class="text-muted-foreground">
									Vendor: {workPackage.purchase_order.vendor.name}
								</p>
							{/if}
							{#if workPackage.purchase_order.description}
								<p class="text-muted-foreground">{workPackage.purchase_order.description}</p>
							{/if}
						</div>
					{:else}
						<p class="text-muted-foreground text-sm">No purchase order assigned</p>
					{/if}
				</div>
			</Card.Content>
		</Card.Root>

		<!-- Child Work Packages -->
		{#if childWorkPackages.length > 0}
			<Card.Root>
				<Card.Header>
					<Card.Title>Child Work Packages</Card.Title>
				</Card.Header>
				<Card.Content>
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Name</TableHead>
								<TableHead>Description</TableHead>
								<TableHead>Created</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{#each childWorkPackages as child}
								<TableRow>
									<TableCell class="font-medium">
										<button
											onclick={() => handleChildView(child.work_package_id)}
											class="text-left hover:underline"
										>
											{child.name}
										</button>
									</TableCell>
									<TableCell class="text-muted-foreground">
										{child.description || '—'}
									</TableCell>
									<TableCell class="text-muted-foreground">
										{formatDate(child.created_at)}
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</Table>
				</Card.Content>
			</Card.Root>
		{/if}
	</div>
</div>

<!-- Delete Confirmation Dialog -->
<Dialog.Root bind:open={deleteDialogOpen}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Delete Work Package</Dialog.Title>
			<Dialog.Description>
				Are you sure you want to delete "{workPackage.name}"? This action cannot be undone.
				{#if childWorkPackages.length > 0}
					<br /><br />
					<strong>Warning:</strong> This work package has {childWorkPackages.length} child work package{childWorkPackages.length ===
					1
						? ''
						: 's'}. You must delete or reassign them first.
				{/if}
			</Dialog.Description>
		</Dialog.Header>
		<Dialog.Footer>
			<Button variant="outline" onclick={() => (deleteDialogOpen = false)}>Cancel</Button>
			<form method="POST" action="?/delete" use:enhance>
				<Button type="submit" variant="destructive" disabled={childWorkPackages.length > 0}>
					Delete
				</Button>
			</form>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
