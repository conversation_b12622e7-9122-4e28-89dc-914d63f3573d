import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { workPackageSchema } from '$lib/schemas/work_package';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { error } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ params, locals, cookies }) => {
	const { user } = await requireUser(cookies);
	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject(params, cookies);

	// Get project information
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		throw error(404, 'Project not found');
	}

	// Fetch purchase orders for selection
	const { data: purchaseOrders, error: purchaseOrdersError } = await supabase.rpc(
		'get_purchase_orders_for_work_package',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (purchaseOrdersError) {
		console.error('Error fetching purchase orders:', purchaseOrdersError);
		throw error(500, 'Failed to fetch purchase orders');
	}

	// Fetch WBS library items for selection
	const { data: wbsItems, error: wbsItemsError } = await supabase.rpc(
		'get_wbs_items_for_work_package',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (wbsItemsError) {
		console.error('Error fetching WBS items:', wbsItemsError);
		throw error(500, 'Failed to fetch WBS items');
	}

	// Fetch work packages for parent selection
	const { data: parentWorkPackages, error: parentWorkPackagesError } = await supabase.rpc(
		'get_work_packages_for_parent_selection',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (parentWorkPackagesError) {
		console.error('Error fetching parent work packages:', parentWorkPackagesError);
		throw error(500, 'Failed to fetch parent work packages');
	}

	const form = await superValidate(zod(workPackageSchema));

	return {
		form,
		project: projectData,
		purchaseOrders: purchaseOrders || [],
		wbsItems: wbsItems || [],
		parentWorkPackages: parentWorkPackages || [],
	};
};

export const actions: Actions = {
	default: async ({ request, locals, cookies, params }) => {
		const { user } = await requireUser(cookies);
		const { supabase } = locals;
		const { org_name, client_name, project_name } = requireProject(params, cookies);

		const form = await superValidate(request, zod(workPackageSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Get project_id
		const { data: projectData, error: projectError } = await supabase
			.from('project')
			.select('*, client!inner(name, organization(name, org_id))')
			.eq('client.organization.name', org_name)
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (projectError || !projectData) {
			console.error('Error fetching project:', projectError);
			return fail(404, { form, message: { type: 'error', text: 'Project not found' } });
		}

		// Create the work package
		const { data: workPackage, error: workPackageError } = await supabase
			.from('work_package')
			.insert({
				...form.data,
				project_id: projectData.project_id,
			})
			.select('work_package_id, name')
			.single();

		if (workPackageError) {
			console.error('Error creating work package:', workPackageError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create work package' },
			});
		}

		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
				client_name,
			)}/projects/${encodeURIComponent(project_name)}/work-package`,
			{
				type: 'success',
				message: `Work package "${workPackage.name}" created successfully`,
			},
			cookies,
		);
	},
};
